# 🛠️ GUIDE D'INTERVENTION MANUELLE - CISCO

**Version:** 1.0  
**Date:** 15/08/2025  
**Objectif:** Permettre à Cisco d'intervenir rapidement sur les paramètres clés sans passer par l'agent

---

## 🎬 VITESSE DES SLIDES (VOLETS CINÉMATOGRAPHIQUES)

### 📍 **Fichier:** `Components/Cinema/CinemaController.tsx`

#### **Ouverture des volets (lignes 98-105)**
```typescript
// Animation des volets qui glissent vers les côtés (ralentie selon demande CISCO)
timeline
  .to(leftCurtainRef.current, {
    x: '-100%',
    duration: 3.5, // 🎬 CISCO: MODIFIER ICI - Actuellement 3.5s
    ease: 'power2.inOut'
  }, 0)
  .to(rightCurtainRef.current, {
    x: '100%',
    duration: 3.5, // 🎬 CISCO: MODIFIER ICI - Actuellement 3.5s
    ease: 'power2.inOut'
  }, 0);
```

#### **Fermeture des volets (lignes 127-134)**
```typescript
// Retour des volets à leur position initiale (ralenti selon demande CISCO)
timeline
  .to(leftCurtainRef.current, {
    x: '0%',
    duration: 3.5, // 🎬 CISCO: MODIFIER ICI - Actuellement 3.5s
    ease: 'power2.inOut'
  }, 0)
  .to(rightCurtainRef.current, {
    x: '0%',
    duration: 3.5, // 🎬 CISCO: MODIFIER ICI - Actuellement 3.5s
    ease: 'power2.inOut'
  }, 0);
```

**💡 CONSEIL:** 
- Plus rapide : 2.0s
- Normal : 3.5s (actuel)
- Plus lent : 5.0s

---

## 🎵 VOLUMES AUDIO D'AMBIANCE

### 📍 **Fichier:** `Components/Audio/AmbientSoundManagerV2.tsx`

#### **Configuration des volumes (lignes 25-65)**
```typescript
const SOUND_CONFIG = {
  // 🌌 Nuit - Volume actuel: 0.4
  night: { 
    sounds: ['night-crickets.mp3'], 
    volume: 0.4, // 🎵 CISCO: MODIFIER ICI (0.1 = très bas, 1.0 = max)
    folder: 'nuit'
  },

  // 🌅 Aube - Volume actuel: 0.3 (réduit car merle trop fort)
  dawn: { 
    sounds: ['village_morning_birds_roosters.mp3'], 
    volume: 0.3, // 🎵 CISCO: MODIFIER ICI (réduit de 0.5 à 0.3)
    folder: 'aube'
  },

  // ☀️ Midi - Volume actuel: 0.8
  midday: { 
    sounds: ['birds-singing.mp3'], 
    volume: 0.8, // 🎵 CISCO: MODIFIER ICI
    folder: 'midi'
  },

  // 🌇 Coucher - Volume actuel: 0.6
  sunset: {
    sounds: ['evening-birds.mp3'],
    volume: 0.6, // 🎵 CISCO: MODIFIER ICI
    folder: 'coucher-soleil'
  }
};
```

**💡 CONSEIL:** 
- Très bas : 0.1-0.2
- Bas : 0.3-0.4 (aube actuel)
- Moyen : 0.5-0.6 (coucher actuel)
- Fort : 0.7-0.8 (midi actuel)
- Très fort : 0.9-1.0

---

## 🎨 DÉGRADÉS DE COULEURS

### 📍 **Fichier:** `Components/Background/DynamicBackground.tsx`

#### **Palettes de couleurs (lignes 28-55)**
```typescript
const BACKGROUND_MODES = {
  // 🌅 AUBE
  dawn: {
    primary: '#ff8a65',    // 🎨 CISCO: Bas (orange rosé) - MODIFIER ICI
    secondary: '#5a6b7d',  // 🎨 CISCO: Milieu (gris bleu) - MODIFIER ICI
    tertiary: '#2d3748'    // 🎨 CISCO: Haut (bleu nuit) - MODIFIER ICI
  },

  // ☀️ MIDI
  midday: {
    primary: '#e3f2fd',    // 🎨 CISCO: Bas (bleu très clair) - MODIFIER ICI
    secondary: '#90caf9',  // 🎨 CISCO: Milieu (bleu clair) - MODIFIER ICI
    tertiary: '#42a5f5'    // 🎨 CISCO: Haut (bleu pur) - MODIFIER ICI
  },

  // 🌇 COUCHER
  sunset: {
    primary: '#ff7043',    // 🎨 CISCO: Bas (orange/rouge) - MODIFIER ICI
    secondary: '#ab7967',  // 🎨 CISCO: Milieu (brun orangé) - MODIFIER ICI
    tertiary: '#5c6bc0'    // 🎨 CISCO: Haut (bleu) - MODIFIER ICI
  },

  // 🌌 NUIT
  night: {
    primary: '#0d1117',    // 🎨 CISCO: Bas (bleu nuit sombre) - MODIFIER ICI
    secondary: '#1c2938',  // 🎨 CISCO: Milieu (bleu nuit) - MODIFIER ICI
    tertiary: '#2d3748'    // 🎨 CISCO: Haut (bleu nuit clair) - MODIFIER ICI
  }
};
```

**💡 CONSEIL:** 
- Utiliser des codes couleur hexadécimaux (#rrggbb)
- primary = horizon (bas de l'écran)
- secondary = milieu du ciel
- tertiary = zénith (haut de l'écran)

---

## ⚡ VITESSES DE TRANSITION

### 📍 **Fichier:** `Components/Background/DynamicBackground.tsx`

#### **Durées de transition (lignes 412, 484, 616)**
```typescript
// Transition des couleurs
const transitionDuration = 25.0; // 🎬 CISCO: MODIFIER ICI (en secondes)

// Transition de l'éclairage
const transitionDuration = 40.0; // 🎬 CISCO: MODIFIER ICI (en secondes)
```

**💡 CONSEIL:** 
- Rapide : 10-15s
- Normal : 25-40s (actuel)
- Lent : 60-90s

---

## 🔧 APRÈS MODIFICATION

**IMPORTANT:** Après chaque modification :

1. **Sauvegarder** le fichier (Ctrl+S)
2. **Recompiler** l'application :
   ```bash
   npm run build
   ```
3. **Redémarrer** le serveur de dev :
   ```bash
   npm run dev
   ```
4. **Tester** les changements sur http://localhost:5173

---

## 📞 SUPPORT

Si problème après modification :
- Vérifier la syntaxe (virgules, accolades)
- Consulter la console du navigateur (F12)
- Revenir aux valeurs précédentes si erreur
- Contacter l'agent Augment pour assistance

---

**FIN DU GUIDE** 🎯
